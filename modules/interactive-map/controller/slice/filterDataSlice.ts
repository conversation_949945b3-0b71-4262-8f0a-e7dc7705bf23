import { createSlice, PayloadAction } from "@reduxjs/toolkit"

import type { InteractiveMap } from "@module/interactive-map/common/types"

export const initialState: InteractiveMap.FilterDataSlice = {
  availableCourses: [],
  course: "",
  includeSurroundingArea: false,
  isOnCampus: true,
  isWithAIPTMentor: true,
  searchedLocation: null,
  locationName: "",
  state: "",
  viewMode: "list",
  useCurrentLocation: true,
}

const filterDataSlice = createSlice({
  name: "filter_data",
  initialState,
  reducers: {
    setCourse: (state, action: PayloadAction<string>) => {
      state.course = action.payload
    },
    setViewMode: (state, action: PayloadAction<InteractiveMap.ViewMode>) => {
      state.viewMode = action.payload
    },
    setState: (state, action: PayloadAction<InteractiveMap.State | "">) => {
      console.log('Hello - Redux setState action dispatched with:', action.payload)
      state.useCurrentLocation = false
      state.state = action.payload
    },
    clearState: (state) => {
      console.log('Hello - Redux clearState action dispatched')
      state.state = initialState.state
    },
    setAvailableCourses: (state, action: PayloadAction<GenericList[]>) => {
      state.availableCourses = action.payload
    },
    resetFilterData: (state) => {
      state.course = initialState.course
      state.includeSurroundingArea = initialState.includeSurroundingArea
      state.isOnCampus = initialState.isOnCampus
      state.isWithAIPTMentor = initialState.isWithAIPTMentor
      state.searchedLocation = initialState.searchedLocation
      state.locationName = initialState.locationName
      state.state = initialState.state
      state.useCurrentLocation = initialState.useCurrentLocation
    },
    toggleSurroundingAreas: (state) => {
      state.includeSurroundingArea = !state.includeSurroundingArea
    },
    toggleIsOnCampus: (state) => {
      state.isOnCampus = !state.isOnCampus
    },
    toggleIsWithAIPTMentor: (state) => {
      state.isWithAIPTMentor = !state.isWithAIPTMentor
    },
    toggleUseCurrentLocation: (state) => {
      const current = state.useCurrentLocation
      state.useCurrentLocation = !state.useCurrentLocation

      if (!current) state.state = ""
    },
    setUseCurrentLocation: (state, action: PayloadAction<boolean>) => {
      state.useCurrentLocation = action.payload
    },
  },
})

export const {
  setCourse,
  toggleSurroundingAreas,
  toggleIsOnCampus,
  toggleIsWithAIPTMentor,
  setViewMode,
  setState,
  resetFilterData,
  toggleUseCurrentLocation,
  setUseCurrentLocation,
  setAvailableCourses,
  clearState,
} = filterDataSlice.actions

export default filterDataSlice.reducer
