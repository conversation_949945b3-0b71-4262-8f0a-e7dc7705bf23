"use client"

import React, { ChangeEvent, useRef } from "react"
import { useId } from "@react-aria/utils"
import { styled } from "@module/apricot/theme/stitches"

import { Fieldset, InputWrapper, Label, Radio, RadioButtonWrapper } from "@module/form-elements/ui"

import { useMapDispatch, useMapSelector } from "@module/interactive-map/service/useMapState"
import { setState } from "@module/interactive-map/controller/slice/filterDataSlice"
import { setCustomPosition } from "@module/redux/slice/user-location"
import { gotoPage } from "@module/pagination/controller/paginationSlice"
import { zoomToDefaultCenter } from "@module/interactive-map/controller/slice/mapSlice"

import type { InteractiveMap } from "@module/interactive-map/common/types"

const allStates: InteractiveMap.State[] = ["ACT", "NSW", "VIC", "QLD", "WA", "SA", "TAS", "NT"]

const StateFieldset = styled(Fieldset, {
  display: "flex",
  alignItems: "center",
  gap: "$xsm",
  maxWidth: 720,
  background: "white",
  border: "1px solid $border",
  br: "$sm",
  p: "$xxsm $sm",
  "@max-lg": {
    display: "none",
  },
  "@xl": {
    width: "$full",
    gap: "$sm",
  },
  "@max-xl": {
    flexWrap: "wrap",
    maxWidth: 555,
  },
  [`> ${Label}`]: {
    fontSize: 14,
    lineHeight: "16.8px",
    fontFamily: "$body",
    fontWeight: 400,
    maxWidth: 108,
    m: 0,
    "@max-xl": {
      width: "$full",
      maxWidth: "initial",
    },
  },
  [`${Radio} + ${Label}`]: {
    fontSize: "$xsm",
    fontWeight: 600,
  },
})

export const FilterByState = () => {
  const id = useId()
  const dispatch = useMapDispatch()
  const { state } = useMapSelector((state) => state.filter)
  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const onChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedState = event.target.value as InteractiveMap.State

    console.log('Hello - State filter changed to:', selectedState)

    // Set the state filter
    dispatch(setState(selectedState))

    // Clear any custom location since we're filtering by state
    dispatch(setCustomPosition(null))

    // Reset pagination to page 1 when filter changes
    dispatch(gotoPage(0))
  }

  const handleLabelClick = (stateValue: InteractiveMap.State) => {
    // If this state is already selected, handle double-click to clear
    if (state === stateValue) {
      if (clickTimeoutRef.current) {
        // This is a double-click - clear the state
        clearTimeout(clickTimeoutRef.current)
        clickTimeoutRef.current = null

        console.log('Hello - Double-click detected, clearing state filter')

        // Clear the state filter
        dispatch(setState(""))

        // Reset pagination to page 1 when filter changes
        dispatch(gotoPage(0))

        // Zoom map back to default center
        dispatch(zoomToDefaultCenter())

        // Prevent the radio button from being checked
        return
      } else {
        // This is the first click - set a timeout to detect double-click
        clickTimeoutRef.current = setTimeout(() => {
          clickTimeoutRef.current = null
        }, 300) // 300ms window for double-click detection
      }
    }
  }

  const toRadioButtonWrapper = (value: InteractiveMap.State) => {
    const identifier = `${id}:${value}`

    return (
      <InputWrapper inline key={identifier}>
        <RadioButtonWrapper>
          <Radio
            id={identifier}
            name={`${id}:filter-by-state`}
            value={value}
            type="radio"
            checked={state !== null && state === value}
            onChange={onChange}
          />
          <Label
            htmlFor={identifier}
            onClick={() => handleLabelClick(value)}
            style={{ cursor: 'pointer' }}
            title={state === value ? "Double-click to clear filter" : ""}
          >
            {value}
          </Label>
        </RadioButtonWrapper>
      </InputWrapper>
    )
  }

  return (
    <StateFieldset aria-label="Browse by state or territory" className="filter-by-state">
      <Label>Browse by state or territory</Label>
      {allStates.map(toRadioButtonWrapper)}
    </StateFieldset>
  )
}
