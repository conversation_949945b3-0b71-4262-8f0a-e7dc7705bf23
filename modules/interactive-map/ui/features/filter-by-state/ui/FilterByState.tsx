"use client"

import React, { ChangeEvent } from "react"
import { useId } from "@react-aria/utils"
import { styled } from "@module/apricot/theme/stitches"

import { Fieldset, InputWrapper, Label, Radio, RadioButtonWrapper } from "@module/form-elements/ui"

import { useMapDispatch, useMapSelector } from "@module/interactive-map/service/useMapState"
import { setState } from "@module/interactive-map/controller/slice/filterDataSlice"
import { setCustomPosition } from "@module/redux/slice/user-location"
import { gotoPage } from "@module/pagination/controller/paginationSlice"
import { zoomToDefaultCenter } from "@module/interactive-map/controller/slice/mapSlice"

import type { InteractiveMap } from "@module/interactive-map/common/types"

const allStates: InteractiveMap.State[] = ["ACT", "NSW", "VIC", "QLD", "WA", "SA", "TAS", "NT"]

const StateFieldset = styled(Fieldset, {
  display: "flex",
  alignItems: "center",
  gap: "$xsm",
  maxWidth: 720,
  background: "white",
  border: "1px solid $border",
  br: "$sm",
  p: "$xxsm $sm",
  "@max-lg": {
    display: "none",
  },
  "@xl": {
    width: "$full",
    gap: "$sm",
  },
  "@max-xl": {
    flexWrap: "wrap",
    maxWidth: 555,
  },
  [`> ${Label}`]: {
    fontSize: 14,
    lineHeight: "16.8px",
    fontFamily: "$body",
    fontWeight: 400,
    maxWidth: 108,
    m: 0,
    "@max-xl": {
      width: "$full",
      maxWidth: "initial",
    },
  },
  [`${Radio} + ${Label}`]: {
    fontSize: "$xsm",
    fontWeight: 600,
  },
})

export const FilterByState = () => {
  const id = useId()
  const dispatch = useMapDispatch()
  const { state } = useMapSelector((state) => state.filter)

  const onChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedState = event.target.value as InteractiveMap.State

    // Set the state filter
    dispatch(setState(selectedState))

    // Clear any custom location since we're filtering by state
    dispatch(setCustomPosition(null))

    // Reset pagination to page 1 when filter changes
    dispatch(gotoPage(0))
  }

  const handleLabelClick = (stateValue: InteractiveMap.State, event: React.MouseEvent) => {
    // If clicking on already selected state, clear it
    if (state === stateValue) {
      event.preventDefault() // Prevent radio button from being triggered

      // Clear the state filter
      dispatch(setState(""))

      // Reset pagination to page 1 when filter changes
      dispatch(gotoPage(0))

      // Zoom map back to default center
      dispatch(zoomToDefaultCenter())
    }
  }

  const toRadioButtonWrapper = (value: InteractiveMap.State) => {
    const identifier = `${id}:${value}`

    return (
      <InputWrapper inline key={identifier}>
        <RadioButtonWrapper>
          <Radio
            id={identifier}
            name={`${id}:filter-by-state`}
            value={value}
            type="radio"
            checked={state !== null && state === value}
            onChange={onChange}
          />
          <Label
            htmlFor={identifier}
            onClick={(event: React.MouseEvent) => handleLabelClick(value, event)}
            style={{ cursor: "pointer" }}
            title={state === value ? "Click again to clear filter" : ""}
          >
            {value}
          </Label>
        </RadioButtonWrapper>
      </InputWrapper>
    )
  }

  return (
    <StateFieldset aria-label="Browse by state or territory" className="filter-by-state">
      <Label>Browse by state or territory</Label>
      {allStates.map(toRadioButtonWrapper)}
    </StateFieldset>
  )
}
