import { useEffect } from "react"
import { useMapDispatch, useMapSelector } from "@module/interactive-map/service/useMapState"
import { zoomIntoState, zoomToDefaultCenter } from "@module/interactive-map/controller/slice/mapSlice"

export default function useMapStateWatcher() {
  const { state } = useMapSelector((state) => state.filter)
  const dispatch = useMapDispatch()

  useEffect(() => {
    if (state === "") {
      dispatch(zoomToDefaultCenter())
      return
    }

    dispatch(zoomIntoState(state))
  }, [state, dispatch])
}
