import { useMemo } from "react"
import { sortBy } from "lodash"

import distanceToPoint from "@module/mapbox/utility/distanceToPoint"
import { ONE_HUNDRED_KMS } from "@module/mapbox/common/constants"

import { useMapSelector } from "@module/interactive-map/service/useMapState"

// Helper functions for filtering
const isApartOfCourses =
  (courses: string[]) => (item: { coursesOffered?: string[]; qualifications?: { id: string; title: string }[] }) => {
    if (courses.length === 0) return true

    // Handle Locations: coursesOffered is string[] (array of course IDs)
    if (item.coursesOffered) {
      return item.coursesOffered.some((offeredId) => courses.includes(offeredId))
    }

    // Handle Mentors: qualifications is {id, title}[]
    if (item.qualifications) {
      return item.qualifications.some((qualification) => courses.includes(qualification.id))
    }

    return false
  }

const isSelectedDeliveryType =
  (isOnCampus: boolean, isWithAIPTMentor: boolean) => (item: { isOnCampus: boolean; isWithAIPTMentor: boolean }) => {
    // If both delivery types are selected, show items that match at least one (OR logic)
    if (isOnCampus && isWithAIPTMentor) return item.isOnCampus || item.isWithAIPTMentor

    // If only one is selected, filter by that type
    if (isOnCampus) return item.isOnCampus
    if (isWithAIPTMentor) return item.isWithAIPTMentor

    // If neither is selected, show all results
    return true
  }

const isWithinLocationRadius =
  (targetLocation: LatLng | null, includeSurroundingArea: boolean) =>
  (item: { location: { latitude: number; longitude: number } }) => {
    if (!targetLocation) return true

    const radius = includeSurroundingArea ? ONE_HUNDRED_KMS * 2 : ONE_HUNDRED_KMS
    const distance = distanceToPoint(targetLocation, item.location)
    return distance <= radius
  }

export default function useMapLocations() {
  const { locations, mentors } = useMapSelector((state) => state.locationData)

  // Read from current filter state for dynamic updates
  const { course, isOnCampus, isWithAIPTMentor, state, includeSurroundingArea, useCurrentLocation } = useMapSelector(
    (state) => state.filter,
  )

  // Get location from userLocation slice
  const { customLocation } = useMapSelector((state) => state.userLocation)

  // Determine the actual location to use for filtering
  const location = useCurrentLocation ? null : customLocation

  return useMemo(() => {
    console.log('Hello - useMapLocations filtering with state:', state)

    // Convert course string to array for filtering
    const courseArray = course && course !== "" ? [course] : []

    // Filter locations
    let filteredLocations = locations
      .filter(isApartOfCourses(courseArray))
      .filter(isSelectedDeliveryType(isOnCampus, isWithAIPTMentor))

    // Filter mentors
    let filteredMentors = mentors
      .filter(isApartOfCourses(courseArray))
      // Mentors are always "withAIPTMentor", so only filter them out if that option is deselected
      .filter(() => isWithAIPTMentor)

    // If a state is selected, filter by state (this takes precedence over location)
    if (state) {
      console.log('Hello - Filtering locations by state:', state)
      console.log('Hello - Locations before state filter:', filteredLocations.length)
      filteredLocations = filteredLocations.filter((loc) => loc.state === state)
      console.log('Hello - Locations after state filter:', filteredLocations.length)

      console.log('Hello - Mentors before state filter:', filteredMentors.length)
      filteredMentors = filteredMentors.filter((mentor) => mentor.state && mentor.state === state)
      console.log('Hello - Mentors after state filter:', filteredMentors.length)
    } else if (location) {
      // If no state is selected but location is provided, filter by location radius
      filteredLocations = filteredLocations.filter(isWithinLocationRadius(location, includeSurroundingArea))
      filteredMentors = filteredMentors.filter(isWithinLocationRadius(location, includeSurroundingArea))
    }

    const finalResults = [...filteredLocations, ...filteredMentors]

    // Sort by distance if location is provided
    if (location) {
      return finalResults.sort((a, b) => {
        const aDistance = distanceToPoint(a.location, location)
        const bDistance = distanceToPoint(b.location, location)
        return aDistance - bDistance
      })
    }

    // Otherwise sort alphabetically
    return sortBy(finalResults, (item) => item.id)
  }, [locations, mentors, state, course, isOnCampus, isWithAIPTMentor, location, includeSurroundingArea])
}
